package com.rj.ecommerce_backend.securityconfig.config

import com.rj.ecommerce_backend.securityconfig.config.AuthTokenFilter
import com.rj.ecommerce_backend.securityconfig.service.LogoutService
import com.rj.ecommerce_backend.securityconfig.service.UserDetailsServiceImpl
import jakarta.servlet.http.HttpServletResponse
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
class SecurityConfig(
    private val userDetailsService: UserDetailsServiceImpl,
    private val authTokenFilter: AuthTokenFilter,
    private val logoutService: LogoutService
) {
    // ... (authenticationManager, passwordEncoder, authenticationProvider beans) ...
    @Bean
    fun authenticationManager(authConfiguration: AuthenticationConfiguration): AuthenticationManager {
        return authConfiguration.authenticationManager
    }

    @Bean
    fun passwordEncoder(): PasswordEncoder {
        return BCryptPasswordEncoder()
    }

    @Bean
    fun authenticationProvider(): DaoAuthenticationProvider {
        return DaoAuthenticationProvider().apply {
            setUserDetailsService(userDetailsService)
            setPasswordEncoder(passwordEncoder())
        }
    }


    @Bean
    fun filterChain(http: HttpSecurity): SecurityFilterChain {
        http { // This top-level block uses an invoke operator
            cors { // 'this' is CorsConfigurer
                configurationSource = corsConfigurationSource()
            }
            csrf { // 'this' is CsrfConfigurer
                disable()
            }
            exceptionHandling { // 'this' is ExceptionHandlingConfigurer
                authenticationEntryPoint { _, response, authException ->
                    response.sendError(
                        HttpServletResponse.SC_UNAUTHORIZED,
                        "Authentication Failed: ${authException.message}"
                    )
                }
                // accessDeniedHandler { /* ... */ }
            }
            sessionManagement { // 'this' is SessionManagementConfigurer
                sessionCreationPolicy = SessionCreationPolicy.STATELESS
            }
            authorizeHttpRequests { // 'this' is AuthorizeHttpRequestsConfigurer.AuthorizationManagerRequestMatcherRegistry
                authorize("/actuator/prometheus", permitAll)
                authorize("/api/v1/auth/**", permitAll)
                authorize("/swagger-ui.html", permitAll)
                authorize("/swagger-ui/**", permitAll)
                authorize("/api-docs", permitAll)
                authorize("/api-docs/**", permitAll)
                authorize("/api/v1/public/**", permitAll)
                authorize("/actuator/**", hasRole("ADMIN"))
                authorize(HttpMethod.GET, "/api/v1/users/**", authenticated)
                authorize(HttpMethod.PUT, "/api/v1/users/**", authenticated)
                authorize(HttpMethod.DELETE, "/api/v1/users/**", authenticated)
                authorize("/api/v1/admin/**", hasRole("ADMIN"))
                authorize(anyRequest, authenticated)
            }
            authenticationProvider(authenticationProvider()) // Method call on HttpSecurityDsl / HttpSecurity
            addFilterBefore<UsernamePasswordAuthenticationFilter>(authTokenFilter) // Method call

            logout { // 'this' inside this block should be LogoutConfigurer
                logoutUrl = "/api/v1/auth/logout" // Property access on LogoutConfigurer
                addLogoutHandler(logoutService)   // Method call on LogoutConfigurer
                this.logoutSuccessHandler { _, response, _ ->
                    SecurityContextHolder.clearContext()
                    response.status = HttpServletResponse.SC_OK
                }
                // invalidateHttpSession(true)
                // deleteCookies("JSESSIONID")
            }
        }
        return http.build()
    }

    @Bean
    fun corsConfigurationSource(): CorsConfigurationSource {
        val configuration = CorsConfiguration().apply {
            allowedOrigins = listOf("http://localhost:3000")
            allowedMethods = listOf("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS")
            allowedHeaders = listOf("Authorization", "Content-Type", "X-Requested-With", "X-CSRF-TOKEN")
            exposedHeaders = listOf("Authorization", "Location")
            allowCredentials = true
            maxAge = 3600L
        }
        val source = UrlBasedCorsConfigurationSource().apply {
            registerCorsConfiguration("/api/v1/**", configuration)
        }
        return source
    }
}