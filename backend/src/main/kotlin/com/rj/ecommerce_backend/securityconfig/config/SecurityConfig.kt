package com.rj.ecommerce_backend.securityconfig.config // Or your chosen package

import com.rj.ecommerce_backend.securityconfig.service.LogoutService // Assuming LogoutService is Kotlin
import com.rj.ecommerce_backend.securityconfig.service.UserDetailsServiceImpl // Your Kotlin UserDetailsServiceImpl
import jakarta.servlet.http.HttpServletResponse
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity // For @PreAuthorize etc.
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer // For csrf disable
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.context.SecurityContextHolder // For logout success handler
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource
import org.springframework.security.config.annotation.web.invoke

@Configuration
@EnableWebSecurity // Enables Spring Security's web security support
@EnableMethodSecurity(prePostEnabled = true) // Enables @PreAuthorize, @PostAuthorize, etc.
class SecurityConfig( // Dependencies injected via primary constructor
    private val userDetailsService: UserDetailsServiceImpl, // Your Kotlin UserDetailsServiceImpl
    private val authTokenFilter: AuthTokenFilter, // Your Kotlin AuthTokenFilter
    private val logoutService: LogoutService // Your Kotlin LogoutService
) {

    @Bean
    fun authenticationManager(authConfiguration: AuthenticationConfiguration): AuthenticationManager {
        return authConfiguration.authenticationManager
    }

    @Bean
    fun passwordEncoder(): PasswordEncoder {
        return BCryptPasswordEncoder()
    }

    @Bean
    fun authenticationProvider(): DaoAuthenticationProvider {
        // Using 'apply' scope function for concise object configuration
        return DaoAuthenticationProvider().apply {
            setUserDetailsService(userDetailsService)
            setPasswordEncoder(passwordEncoder())
        }
    }

    @Bean
    fun filterChain(http: HttpSecurity): SecurityFilterChain {
        http { // Using Spring Security Kotlin DSL
            cors { // Configure CORS
                configurationSource = corsConfigurationSource()
            }
            csrf { // Disable CSRF
                disable() // Modern way using Kotlin DSL: http.csrf(AbstractHttpConfigurer::disable) also works
            }
            // Explicitly call exceptionHandling() and pass a lambda
            http.exceptionHandling { configurer -> // Pass the lambda to the method
                configurer.authenticationEntryPoint { _, response, authException ->
                    response.sendError(
                        HttpServletResponse.SC_UNAUTHORIZED,
                        "Authentication Failed: ${authException.message}"
                    )
                }
            }
            sessionManagement { // Set session management to stateless
                sessionCreationPolicy = SessionCreationPolicy.STATELESS
            }

            authorizeHttpRequests { // Configure authorization rules
                // Public endpoints
                authorize("/actuator/prometheus", permitAll)
                authorize("/api/v1/auth/**", permitAll)
                authorize("/swagger-ui.html", permitAll)
                authorize("/swagger-ui/**", permitAll)
                authorize("/api-docs", permitAll)
                authorize("/api-docs/**", permitAll)
                authorize("/api/v1/public/**", permitAll)

                // Admin restricted actuator endpoints
                authorize("/actuator/**", hasRole("ADMIN"))

                // Authenticated user endpoints (specific to users resource for now)
                authorize(
                    HttpMethod.GET,
                    "/api/v1/users/**",
                    authenticated
                ) // More specific rules can be added via @PreAuthorize
                authorize(HttpMethod.PUT, "/api/v1/users/**", authenticated) // on controller methods
                authorize(HttpMethod.DELETE, "/api/v1/users/**", authenticated)
                // Add other user-specific paths here if needed, e.g., /api/v1/orders for current user

                // Admin restricted areas
                authorize("/api/v1/admin/**", hasRole("ADMIN"))

                // Default: any other request needs to be authenticated
                authorize(anyRequest, authenticated)
            }
            authenticationProvider() // Set the custom authentication provider
            addFilterBefore<UsernamePasswordAuthenticationFilter>(authTokenFilter) // Add JWT filter
            logout { // Configure logout
                logoutUrl = "/api/v1/auth/logout"
                addLogoutHandler(logoutService) // Custom logout handler (e.g., invalidate token)
                logoutSuccessHandler { request, response, authentication ->
                    // Clear security context holder explicitly if needed, though Spring does a lot
                    SecurityContextHolder.clearContext()
                    response.status = HttpServletResponse.SC_OK // Set 200 OK status
                    // response.writer.write("Logout successful") // Optional: send a message
                    // logger.info { "User logout successful." + (authentication?.name?.let { " User: $it" } ?: "") }
                }
                // invalidateHttpSession(true) // Not strictly needed for STATELESS, but good for thoroughness
                // deleteCookies("JSESSIONID") // If any session cookies were somehow created
            }
        }
        return http.build()
    }

    @Bean
    fun corsConfigurationSource(): CorsConfigurationSource {
        val configuration = CorsConfiguration().apply {
            allowedOrigins = listOf("http://localhost:3000") // Or use properties for this
            allowedMethods = listOf("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS")
            allowedHeaders = listOf(
                "Authorization",
                "Content-Type",
                "X-Requested-With",
                "X-CSRF-TOKEN"
            ) // Added X-CSRF-TOKEN just in case, though CSRF is disabled
            exposedHeaders = listOf("Authorization", "Location") // Added Location for 201 CREATED responses
            allowCredentials = true
            maxAge = 3600L // Optional: How long the response from a pre-flight request can be cached
        }
        val source = UrlBasedCorsConfigurationSource().apply {
            registerCorsConfiguration("/api/v1/**", configuration) // Apply CORS to your API paths
        }
        return source
    }
}